import type {
  BackendSearchItem,
  DirectGroupedSearchData,
  GroupedSearchData,
} from './types';

import type { VbenFormSchema } from '#/adapter/form';

import { useUploadStore } from '#/stores/upload';
import { normalizeOptions } from '#/utils/options';
import { createBatchUploadWithStore } from '#/utils/upload/createBatchUploadFunction';

import {
  createApiSelectPaginatedFunction,
  createListActionFunction,
  createSearchableListActionFunction,
  defaultFilterTreeNode,
} from './api-request';
import { LinkageRuleConverter } from './linkage-converter';
import { TYPE_TO_COMPONENT_MAP } from './types';

// 全局状态：跟踪表单是否正在进行初始化设置
const formInitializationState = new Map<any, boolean>();

/**
 * 设置表单初始化状态
 * @param formApi 表单API实例
 * @param isInitializing 是否正在初始化
 */
export function setFormInitializationState(
  formApi: any,
  isInitializing: boolean,
) {
  if (formApi) {
    formInitializationState.set(formApi, isInitializing);

    // 如果设置为初始化状态，3秒后自动清除（防止状态泄漏）
    if (isInitializing) {
      setTimeout(() => {
        formInitializationState.delete(formApi);
      }, 3000);
    }
  }
}

/**
 * 检查表单是否正在初始化
 * @param formApi 表单API实例
 * @returns 是否正在初始化
 */
export function isFormInitializing(formApi: any): boolean {
  return formInitializationState.get(formApi) === true;
}

/**
 * 创建分组标题的 schema
 * @param label 分组标题
 * @returns 分组标题 schema
 */
function createGroupDivider(label: string): VbenFormSchema {
  return {
    fieldName: `groupTitle_${label}`,
    component: 'GroupTitle',
    formItemClass: 'col-span-3',
    hideLabel: true,
    componentProps: {
      title: label,
    },
  } as any;
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema
 * @param data 后端数据（新格式），支持分组结构或简单数组
 * @param options 转换选项
 * @param options.enableGrouping 是否启用分组功能，默认为 true
 * @param options.formMode 表单模式：'add' | 'edit' | undefined，用于控制字段的编辑权限
 * @returns 前端表单 Schema 数组
 */
export function transformBackendSearchToSchema(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  options: { enableGrouping?: boolean; formMode?: 'add' | 'edit' } = {},
): VbenFormSchema[] {
  const { enableGrouping = true, formMode } = options;
  const newSchema: VbenFormSchema[] = [];
  // 如果是简单数组格式
  if (Array.isArray(data)) {
    // 检查是否是分组数组格式（数组中的元素包含 label 和 dataItem）
    if (data.length > 0 && data[0] && 'dataItem' in data[0]) {
      // 处理直接的分组数组格式
      (data as GroupedSearchData[]).forEach((group) => {
        // 转换分组内的字段
        if (group.dataItem && Array.isArray(group.dataItem)) {
          const groupItems = group.dataItem
            .map((item) => transformSingleItem(item, formMode, group.dataItem))
            .filter((item): item is VbenFormSchema => item !== null);

          // 只有当分组内有有效字段时，才添加分组标题和字段
          if (groupItems.length > 0) {
            // 添加分组标题（如果有 label 且启用分组）
            if (group.label && enableGrouping) {
              newSchema.push(createGroupDivider(group.label));
            }
            newSchema.push(...groupItems);
          }
        }
      });
      return newSchema;
    } else {
      // 处理简单数组格式
      const backendItems = data as BackendSearchItem[];
      const items = backendItems
        .map((item) => transformSingleItem(item, formMode, backendItems))
        .filter((item): item is VbenFormSchema => item !== null);
      newSchema.push(...items);
    }
  }

  // 如果是包装在 schema 属性中的分组格式
  if ('schema' in data && Array.isArray(data.schema)) {
    (data as DirectGroupedSearchData).schema.forEach((group) => {
      // 转换分组内的字段
      if (group.dataItem && Array.isArray(group.dataItem)) {
        const groupItems = group.dataItem
          .map((item) => transformSingleItem(item, formMode, group.dataItem))
          .filter((item): item is VbenFormSchema => item !== null);

        // 只有当分组内有有效字段时，才添加分组标题和字段
        if (groupItems.length > 0) {
          // 添加分组标题（如果有 label 且启用分组）
          if (group.label && enableGrouping) {
            newSchema.push(createGroupDivider(group.label));
          }
          newSchema.push(...groupItems);
        }
      }
    });
  }

  return newSchema;
}

/**
 * 为表单 API 设置初始化跟踪的包装函数
 * @param formApi 表单 API
 * @param schema 表单 schema
 */
export function setSchemaWithTracking(formApi: any, schema: any[]) {
  if (!formApi) {
    return;
  }

  // 设置 schema
  formApi.setState({ schema });
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema（带分组）
 * @param data 后端数据
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 前端表单 Schema 数组（包含分组标题）
 */
export function transformBackendSearchToSchemaWithGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: true,
    formMode,
  });
}

/**
 * 将后端搜索条件数据转换为前端表单 Schema（不分组）
 * @param data 后端数据
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 前端表单 Schema 数组（不包含分组标题）
 */
export function transformBackendSearchToSchemaWithoutGrouping(
  data: BackendSearchItem[] | DirectGroupedSearchData | GroupedSearchData[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(data, {
    enableGrouping: false,
    formMode,
  });
}

/**
 * 检查值是否有效（不为 undefined、null、空字符串）
 */
function hasValue(value: any): boolean {
  return value !== undefined && value !== null && value !== '';
}

/**
 * 触发远程组件的API请求刷新
 * @param targetFieldName 目标字段名
 * @param targetValue 赋值的值
 * @param values 表单所有字段的值
 * @param backendItems 后端配置数据
 * @param isUserInteraction 是否为用户交互触发（默认为true）
 *   - true: 用户主动选择触发，会刷新远程组件的API
 *   - false: 回显数据触发，跳过API刷新避免不必要的请求
 */
function triggerRemoteComponentRefresh(
  targetFieldName: string,
  targetValue: any,
  values: Record<string, any>,
  backendItems: BackendSearchItem[],
  isUserInteraction: boolean = true,
) {
  try {
    // 如果不是用户交互（即回显数据），则跳过API刷新
    if (!isUserInteraction) {
      console.log(
        `[triggerRemoteComponentRefresh] 跳过回显数据的API刷新: ${targetFieldName}`,
      );
      return;
    }

    // 查找目标字段的配置
    const targetField = backendItems.find(
      (item) => item.field === targetFieldName,
    );

    if (!targetField) {
      return;
    }

    // 检查是否是远程组件
    const isRemoteComponent =
      targetField.type === 'apiSelect' ||
      targetField.type === 'apiselect' ||
      targetField.type === 'apiTree';

    if (!isRemoteComponent) {
      return;
    }

    const config = targetField.config || {};

    if (config.url || config.api) {
      // 对于有returnParamsField配置的组件，它们通过dependencies.componentProps来动态更新API
      // 这些组件的dependencies.triggerFields包含自身字段，当自身字段值变化时会触发API刷新
      if (config.returnParamsField) {
        // 对于有returnParamsField的组件，联动赋值已经修改了目标字段的值
        // dependencies会自动监听到目标字段值的变化并触发componentProps重新计算
        // 这里不需要额外的操作，因为值的变化已经足够触发API刷新
        return;
      }
      // 对于没有returnParamsField但有API配置的组件，我们需要手动触发刷新
      // 通过添加一个特殊的刷新标记来触发组件重新渲染和API请求
      const refreshKey = `__${targetFieldName}_api_refresh__`;
      const currentTime = Date.now();

      // 设置刷新标记
      values[refreshKey] = currentTime;

      // 延迟删除刷新标记，给组件足够时间响应
      setTimeout(() => {
        if (values[refreshKey] === currentTime) {
          delete values[refreshKey];
        }
      }, 100);
    } else {
      console.log(
        `[triggerRemoteComponentRefresh] 远程组件 ${targetFieldName} 没有API配置，跳过刷新`,
      );
    }
  } catch (error) {
    console.warn(`触发远程组件API刷新失败: ${targetFieldName}`, error);
  }
}

/**
 * 转换 options 配置 - 使用统一的选项处理函数
 * @param options 选项配置
 * @param fieldName 字段名（用于错误提示）
 * @param fieldType 字段类型（用于错误提示）
 * @returns 标准化的选项数组
 */
function transformOptions(
  options: any,
  fieldName: string,
  fieldType: string,
): Array<{ disabled?: boolean; label: string; value: any }> {
  return normalizeOptions(options, {
    fieldName,
    fieldType,
    componentName: 'transformBackendSearchToSchema',
  });
}

/**
 * 深度清理对象中的 undefined 和 null 值
 * @param obj 要清理的对象
 * @returns 清理后的对象
 */
function cleanUndefinedValues<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const cleaned = {} as T;

  for (const [key, value] of Object.entries(obj)) {
    // 跳过 undefined 和 null 值
    if (value === undefined || value === null) {
      continue;
    }

    // 如果是数组，递归清理数组中的每个元素
    if (Array.isArray(value)) {
      const cleanedArray = value
        .map((item) =>
          typeof item === 'object' && item !== null
            ? cleanUndefinedValues(item)
            : item,
        )
        .filter((item) => item !== undefined && item !== null);

      if (cleanedArray.length > 0) {
        (cleaned as any)[key] = cleanedArray;
      }
    }
    // 如果是对象，递归清理
    else if (typeof value === 'object') {
      const cleanedObject = cleanUndefinedValues(value);
      // 只有当清理后的对象不为空时才添加
      if (Object.keys(cleanedObject).length > 0) {
        (cleaned as any)[key] = cleanedObject;
      }
    }
    // 其他类型的值直接添加
    else {
      (cleaned as any)[key] = value;
    }
  }

  return cleaned;
}

/**
 * 转换单个搜索条件
 * @param item 后端搜索条件（新格式）
 * @param formMode 表单模式：'add' | 'edit' | undefined（默认为 undefined，表示不区分模式）
 * @param backendItems 完整的后端配置数据（用于联动赋值时查找目标字段配置）
 * @returns 前端表单 schema，如果字段应该被剔除则返回 null
 */
function transformSingleItem(
  item: BackendSearchItem,
  formMode?: 'add' | 'edit',
  backendItems?: BackendSearchItem[],
): null | VbenFormSchema {
  // 处理 ifShow 为 false 的情况：直接剔除该字段
  if (item.ifShow !== undefined) {
    if (typeof item.ifShow === 'boolean' && item.ifShow === false) {
      // ifShow 为 false 时，直接剔除该字段，不加入转换规则
      return null;
    }
    if (typeof item.ifShow === 'function') {
      // 如果是函数，可以在这里进行预判断（可选）
      // 注意：这里只是示例，实际项目中可能需要根据具体情况决定是否预判断
    }
  }

  const component = TYPE_TO_COMPONENT_MAP[item.type] || 'Input';
  // 创建 config 的副本，避免修改原始数据
  const config = { ...item.config };

  // 处理编辑权限控制
  if (config.editPermission && formMode) {
    const shouldDisable =
      (config.editPermission === 'add-only' && formMode === 'edit') ||
      (config.editPermission === 'edit-only' && formMode === 'add') ||
      config.editPermission === 'none';

    if (shouldDisable) {
      // 如果当前模式下不允许编辑，设置为禁用状态
      // 注意：这里不直接返回 null，而是设置 disabled，这样字段仍然显示但不可编辑
      config.disabled = true;
    }
  }

  // 基础 schema - 只包含有值的属性
  const schema: VbenFormSchema = {
    fieldName: item.field,
    label: item.title,
    component,
  };

  // 只有当属性有值时才添加到 schema 中
  if (hasValue(item.default)) {
    schema.defaultValue = item.default;
  }

  // 处理 formItemClass
  if (hasValue(config.formItemClass)) {
    schema.formItemClass = config.formItemClass;
  } else if (item.type === 'hidden') {
    // 为 hidden 类型添加默认的隐藏样式类
    schema.formItemClass = 'hidden';
  } else {
    // 为普通字段设置默认的宽度样式，适配响应式布局
    // 根据表单默认布局 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3' 设置合适的宽度
    // 小屏：全宽(1/1)，中屏：全宽(1/2)，大屏：全宽(1/3) - 这样每个字段都占满可用宽度
    schema.formItemClass = 'col-span-1';
  }

  // 构建组件属性 - 只包含有值的属性
  const componentProps: Record<string, any> = {};

  // 为所有组件统一添加 w-full 类，确保宽度一致性
  componentProps.class = 'w-full';

  // 添加自定义组件属性（如果有值）
  if (config.componentProps && Object.keys(config.componentProps).length > 0) {
    // 如果自定义属性中有 class，则合并而不是覆盖
    if (config.componentProps.class) {
      componentProps.class = `w-full ${config.componentProps.class}`;
    }
    // 添加其他自定义属性
    Object.assign(componentProps, {
      ...config.componentProps,
      class: componentProps.class, // 确保 class 不被覆盖
    });
  }

  // 根据类型设置特定属性
  switch (item.type) {
    case 'apiSelect':
    case 'apiselect': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      // 如果配置了搜索相关属性，默认启用搜索
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : !!(config.searchable || config.url);
      // 如果使用 API 搜索，禁用本地过滤
      componentProps.filterOption = hasValue(config.filterOption)
        ? config.filterOption
        : !config.url;

      // 多选模式：将 multiple: true 转换为 mode: 'multiple'
      if (config.multiple === true) {
        componentProps.mode = 'multiple';
      } else if (hasValue(config.mode)) {
        componentProps.mode = config.mode;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 处理 API 相关配置
      if (hasValue(config.url) && config.url) {
        // 使用 url 而不是 api，支持搜索功能
        // 检查是否启用了搜索功能且不是禁用搜索请求
        if (
          (config.showSearch || config.searchable) &&
          config.disableSearchRequest !== true
        ) {
          // 创建支持搜索和分页的 API 函数
          const searchParamName =
            config.searchParamName || config.searchFieldName || 'search';

          // 使用支持 ApiSelect 滚动加载的 API 函数
          const finalPageSize =
            config.pagesize ||
            config.pageSize ||
            config.params?.pageSize ||
            config.params?.pagesize ||
            20;
          const apiConfig = {
            pageSize: finalPageSize,
            pageParamName: 'page',
            pageSizeParamName: 'pageSize',
            searchParamName,
          };

          componentProps.api = createApiSelectPaginatedFunction(
            config.url,
            config.params || {},
            apiConfig,
          );

          // 传递搜索字段名给组件
          if (hasValue(config.searchFieldName)) {
            componentProps.searchFieldName = config.searchFieldName;
          }
          if (hasValue(config.searchParamName)) {
            componentProps.searchParamName = config.searchParamName;
          }
        } else {
          // 普通的 API 函数，类似 listAction 格式
          componentProps.api = createListActionFunction(
            config.url,
            config.params || {},
          );
        }
      } else if (hasValue(config.api)) {
        // 兼容旧的 api 配置
        componentProps.api = config.api;
      }

      // API 相关配置 - 设置默认值
      // 根据实际 API 返回的数据结构，默认使用 name 和 id 字段
      componentProps.labelField = config.labelField || 'name';
      componentProps.valueField = config.valueField || 'id';

      // 只有明确配置了 resultField 才设置，否则让组件使用默认行为
      if (hasValue(config.resultField)) {
        componentProps.resultField = config.resultField;
      }

      // 确保在编辑模式下立即加载数据，以便正确显示标签
      // 如果后端没有传递 immediate 参数，默认设置为 true
      componentProps.immediate = hasValue(config.immediate)
        ? config.immediate
        : true;
      if (hasValue(config.alwaysLoad)) {
        componentProps.alwaysLoad = config.alwaysLoad;
      }
      if (hasValue(config.autoSelect)) {
        componentProps.autoSelect = config.autoSelect;
      }
      if (hasValue(config.beforeFetch)) {
        componentProps.beforeFetch = config.beforeFetch;
      }
      if (hasValue(config.afterFetch)) {
        componentProps.afterFetch = config.afterFetch;
      }

      // 回显数据时的参数字段配置
      if (hasValue(config.returnParamsField)) {
        const returnParamsField = config.returnParamsField;

        // 创建依赖关系，监听当前字段自身的变化
        // returnParamsField 指定的是参数名，当前字段的值会作为该参数的值传递给 API

        // 用于跟踪组件初始化状态的变量
        let isInitializing = true;
        let initializationTimer: NodeJS.Timeout | null = null;

        schema.dependencies = {
          triggerFields: [item.field], // 监听当前字段自身
          componentProps: (values: Record<string, any>) => {
            const currentValue = values[item.field];

            // 检查值的有效性
            const hasValidValue =
              currentValue !== undefined &&
              currentValue !== null &&
              currentValue !== '' &&
              !(typeof currentValue === 'string' && currentValue.trim() === '');

            if (hasValidValue) {
              // 有有效值时，构建新的参数并创建动态 API 函数
              const newParams = {
                ...config.params,
                [returnParamsField]: currentValue,
              };

              const dynamicApi = createApiSelectPaginatedFunction(
                config.url,
                newParams,
              );

              // 如果是初始化阶段，延迟设置immediate为true
              let shouldImmediate = !isInitializing;

              // 设置初始化完成的定时器
              if (isInitializing) {
                if (initializationTimer) {
                  clearTimeout(initializationTimer);
                }
                initializationTimer = setTimeout(() => {
                  isInitializing = false;
                  initializationTimer = null;
                }, 500); // 500ms后认为初始化完成
              }

              console.log(`[returnParamsField] ${item.field} API请求:`, {
                currentValue,
                isInitializing,
                shouldImmediate,
              });

              return {
                params: newParams,
                api: dynamicApi,
                immediate: shouldImmediate,
                key: `${item.field}-${currentValue}-${Date.now()}`,
              };
            }

            // 无有效值时，清空选项不发送请求
            return {
              params: config.params || {},
              immediate: false,
              options: [],
            };
          },
        };

        // 同时设置 returnParamsField 到 componentProps，让组件知道需要监听哪个字段
        componentProps.returnParamsField = returnParamsField;

        // 重要：修改初始的 params，确保在表单初始化时就包含正确的参数结构
        // 这样即使在回显时，API 也会收到正确的参数格式
        componentProps.params = {
          ...config.params,
          [returnParamsField]: '', // 初始值为空，会在依赖关系触发时更新
        };
      }

      break;
    }
    case 'apiTree': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;

      // 多选模式：将 multiple: true 转换为 multiple: true（TreeSelect 使用 multiple 属性）
      if (config.multiple === true) {
        componentProps.multiple = true;
      } else if (hasValue(config.multiple)) {
        componentProps.multiple = config.multiple;
      }

      // 检查是否为多选模式（multiple 或 treeCheckable）
      const isMultipleMode = componentProps.multiple || config.treeCheckable;

      // 如果是多选模式且有默认值，确保默认值是数组格式
      if (isMultipleMode && hasValue(item.default)) {
        if (Array.isArray(item.default)) {
          schema.defaultValue = item.default;
        } else {
          // 如果默认值不是数组，转换为数组
          schema.defaultValue =
            item.default !== null && item.default !== undefined
              ? [item.default]
              : [];
        }
      } else if (hasValue(item.default)) {
        // 单选模式，直接使用默认值
        schema.defaultValue = item.default;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 树形组件特有配置
      if (hasValue(config.treeCheckable)) {
        componentProps.treeCheckable = config.treeCheckable;
      }
      if (hasValue(config.treeCheckStrictly)) {
        componentProps.treeCheckStrictly = config.treeCheckStrictly;
      }

      // 处理 API 相关配置
      if (hasValue(config.url) && config.url) {
        // 检查是否需要高级功能（搜索或分页）
        const needsAdvancedFeatures =
          (config.showSearch || config.searchable || config.pagination) &&
          config.disableSearchRequest !== true;

        if (needsAdvancedFeatures) {
          // 使用搜索 API 函数，类似 listAction 格式，并添加分页相关属性
          const searchParamName =
            config.searchParamName || config.searchFieldName || 'search';
          componentProps.api = createSearchableListActionFunction(
            config.url,
            config.params || {},
            searchParamName,
          );

          // 传递搜索字段名给组件
          if (hasValue(config.searchFieldName)) {
            componentProps.searchFieldName = config.searchFieldName;
          }
          if (hasValue(config.searchParamName)) {
            componentProps.searchParamName = config.searchParamName;
          }

          // 如果启用分页，添加分页相关属性
          if (config.pagination) {
            componentProps.pagination = true;
            componentProps.pageSize = config.pageSize || 20;
            componentProps.loadMore = config.loadMore !== false;
            componentProps.pageParamName = config.pageParamName || 'page';
            componentProps.pageSizeParamName =
              config.pageSizeParamName || 'pageSize';
          }
        } else {
          // 使用基础 API 函数，类似 listAction 格式
          componentProps.api = createListActionFunction(
            config.url,
            config.params || {},
          );
        }
      } else if (hasValue(config.api)) {
        // 兼容旧的 api 配置
        componentProps.api = config.api;
      }

      // API 相关配置 - 设置默认值
      componentProps.labelField = config.labelField || 'label';
      componentProps.valueField = config.valueField || 'value';
      componentProps.childrenField = config.childrenField || 'children';

      // 只有明确配置了 resultField 才设置，否则让组件使用默认行为
      if (hasValue(config.resultField)) {
        componentProps.resultField = config.resultField;
      }

      // 确保在编辑模式下立即加载数据，以便正确显示标签
      // 如果后端没有传递 immediate 参数，默认设置为 true
      componentProps.immediate = hasValue(config.immediate)
        ? config.immediate
        : true;
      if (hasValue(config.alwaysLoad)) {
        componentProps.alwaysLoad = config.alwaysLoad;
      }
      if (hasValue(config.beforeFetch)) {
        componentProps.beforeFetch = config.beforeFetch;
      }
      if (hasValue(config.afterFetch)) {
        componentProps.afterFetch = config.afterFetch;
      }

      // 树形组件特有配置
      if (hasValue(config.treeDefaultExpandAll)) {
        componentProps.treeDefaultExpandAll = config.treeDefaultExpandAll;
      }

      // 默认添加 filterTreeNode 函数，如果没有提供的话
      componentProps.filterTreeNode = hasValue(config.filterTreeNode)
        ? config.filterTreeNode
        : defaultFilterTreeNode;

      // 回显数据时的参数字段配置
      if (hasValue(config.returnParamsField)) {
        const returnParamsField = config.returnParamsField;

        // 创建依赖关系，监听当前字段自身的变化
        // returnParamsField 指定的是参数名，当前字段的值会作为该参数的值传递给 API

        // 用于跟踪组件初始化状态的变量
        let isInitializing = true;
        let initializationTimer: NodeJS.Timeout | null = null;

        schema.dependencies = {
          triggerFields: [item.field], // 监听当前字段自身
          componentProps: (values: Record<string, any>) => {
            const currentValue = values[item.field];

            // 检查值的有效性
            const hasValidValue =
              currentValue !== undefined &&
              currentValue !== null &&
              currentValue !== '' &&
              !(typeof currentValue === 'string' && currentValue.trim() === '');

            if (hasValidValue) {
              // 有有效值时，构建新的参数
              const newParams = {
                ...config.params,
                [returnParamsField]: currentValue,
              };

              // 如果是初始化阶段，延迟设置immediate为true
              let shouldImmediate = !isInitializing;

              // 设置初始化完成的定时器
              if (isInitializing) {
                if (initializationTimer) {
                  clearTimeout(initializationTimer);
                }
                initializationTimer = setTimeout(() => {
                  isInitializing = false;
                  initializationTimer = null;
                }, 500); // 500ms后认为初始化完成
              }

              console.log(`[returnParamsField] ${item.field} API请求:`, {
                currentValue,
                isInitializing,
                shouldImmediate,
              });

              return {
                params: newParams,
                immediate: shouldImmediate,
                key: `${item.field}-${currentValue}-${Date.now()}`,
              };
            }

            // 无有效值时，清空选项不发送请求
            return {
              params: config.params || {},
              immediate: false,
              options: [],
            };
          },
        };

        // 同时设置 returnParamsField 到 componentProps，让组件知道需要监听哪个字段
        componentProps.returnParamsField = returnParamsField;

        // 重要：修改初始的 params，确保在表单初始化时就包含正确的参数结构
        // 这样即使在回显时，API 也会收到正确的参数格式
        componentProps.params = {
          ...config.params,
          [returnParamsField]: '', // 初始值为空，会在依赖关系触发时更新
        };
      }
      break;
    }
    case 'checkbox':
    case 'radio': {
      // 设置选项数据 - 支持数组和对象两种格式
      componentProps.options = transformOptions(
        config.options,
        item.field,
        item.type,
      );

      // radio 特有配置
      if (item.type === 'radio') {
        // 设置默认的按钮样式
        componentProps.optionType = hasValue(config.optionType)
          ? config.optionType
          : 'default'; // 可选值: 'default' | 'button'

        // 设置按钮样式（当 optionType 为 'button' 时）
        if (config.optionType === 'button') {
          componentProps.buttonStyle = hasValue(config.buttonStyle)
            ? config.buttonStyle
            : 'outline'; // 可选值: 'outline' | 'solid'
        }
      }
      break;
    }
    case 'date': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'dateRange': {
      componentProps.placeholder = config.placeholder || [
        '开始日期',
        '结束日期',
      ];
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'edittable': {
      // edittable 类型转换为 EditTable 组件
      schema.component = 'EditTable';

      // edittable 类型独占一行，在所有屏幕尺寸下都占满整行
      schema.formItemClass =
        'col-span-1 sm:col-span-2 md:col-span-3 items-baseline';

      // 将 config 中的所有配置作为 params 传递给 EditTable 组件
      componentProps.params = {
        columns: (config as any).columns || [],
        tabList: (config as any).tabList || [],
        // 将 config 中的所有内容都作为 params 传递
        ...config,
      };

      // 设置默认值为 tabList 数据
      if ((config as any).tabList && Array.isArray((config as any).tabList)) {
        schema.defaultValue = (config as any).tabList;
      }

      break;
    }

    case 'hidden': {
      // 隐藏字段：不显示在界面上，但会包含在表单提交中
      componentProps.style = { display: 'none' };
      break;
    }
    case 'input':
    case 'password':
    case 'text': {
      break;
    }

    case 'mentions': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      // 设置选项数据 - 支持数组和对象两种格式
      if (hasValue(config.options)) {
        componentProps.options = transformOptions(
          config.options,
          item.field,
          item.type,
        );
      }
      break;
    }

    case 'number': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      if (hasValue(config.min)) componentProps.min = config.min;
      if (hasValue(config.max)) componentProps.max = config.max;
      if (hasValue(config.precision))
        componentProps.precision = config.precision;
      break;
    }

    case 'rate': {
      // Rate 组件通常不需要特殊配置
      break;
    }

    case 'select': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;
      componentProps.filterOption = hasValue(config.filterOption)
        ? config.filterOption
        : true;

      // 多选模式：将 multiple: true 转换为 mode: 'multiple'
      if (config.multiple === true) {
        componentProps.mode = 'multiple';
      } else if (hasValue(config.mode)) {
        componentProps.mode = config.mode;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 设置选项数据 - 支持数组和对象两种格式
      if (hasValue(config.options)) {
        componentProps.options = transformOptions(
          config.options,
          item.field,
          item.type,
        );
      }
      break;
    }

    case 'switch': {
      // Switch 组件通常不需要 placeholder
      break;
    }

    case 'textarea': {
      componentProps.placeholder = config.placeholder || `请输入${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'time': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      break;
    }

    case 'tree': {
      componentProps.placeholder = config.placeholder || `请选择${item.title}`;
      componentProps.allowClear = hasValue(config.allowClear)
        ? config.allowClear
        : true;
      componentProps.showSearch = hasValue(config.showSearch)
        ? config.showSearch
        : false;

      // 多选模式：将 multiple: true 转换为 multiple: true（TreeSelect 使用 multiple 属性）
      if (config.multiple === true) {
        componentProps.multiple = true;
      } else if (hasValue(config.multiple)) {
        componentProps.multiple = config.multiple;
      }

      // 多选相关配置
      if (hasValue(config.maxTagCount)) {
        componentProps.maxTagCount = config.maxTagCount;
      }
      if (hasValue(config.maxTagTextLength)) {
        componentProps.maxTagTextLength = config.maxTagTextLength;
      }
      if (hasValue(config.maxTagPlaceholder)) {
        componentProps.maxTagPlaceholder = config.maxTagPlaceholder;
      }

      // 树形组件特有配置
      if (hasValue(config.treeCheckable)) {
        componentProps.treeCheckable = config.treeCheckable;
      }
      if (hasValue(config.treeCheckStrictly)) {
        componentProps.treeCheckStrictly = config.treeCheckStrictly;
      }
      if (hasValue(config.treeDefaultExpandAll)) {
        componentProps.treeDefaultExpandAll = config.treeDefaultExpandAll;
      }

      if (hasValue(config.treeData) && Array.isArray(config.treeData)) {
        componentProps.treeData = config.treeData;
      }
      break;
    }
    case 'Upload': {
      // Upload 组件的默认配置
      const uploadConfig = config as any;
      componentProps.accept = uploadConfig.accept || '.png,.jpg,.jpeg';
      componentProps.action = 'oss/putFile';
      // componentProps.disabled = hasValue(uploadConfig.disabled)
      //   ? uploadConfig.disabled
      //   : false;
      componentProps.maxCount = uploadConfig.maxCount || Infinity;
      componentProps.multiple = hasValue(uploadConfig.multiple)
        ? uploadConfig.multiple
        : true;
      componentProps.showUploadList = hasValue(uploadConfig.showUploadList)
        ? uploadConfig.showUploadList
        : true;
      componentProps.listType = uploadConfig.listType || 'picture-card';

      // 确保 Upload 组件有正确的默认值（空数组）
      if (!hasValue(item.default)) {
        schema.default = [];
      }

      // 使用 Pinia store 管理上传状态
      const { customRequest } = createBatchUploadWithStore(item.field);

      componentProps.customRequest = customRequest;

      // 处理文件列表变化，更新总文件数
      const originalOnChange = componentProps.onChange;
      componentProps.onChange = (info: any) => {
        const { fileList } = info;

        // 确保 fileList 是数组，如果不是则使用空数组
        const safeFileList = Array.isArray(fileList) ? fileList : [];

        // 只计算需要上传的文件数量（排除已完成的文件）
        const pendingFiles = safeFileList.filter(
          (file: any) => file.status === 'uploading' || !file.status,
        );

        // 计算已完成的文件数量
        const completedFiles = safeFileList.filter(
          (file: any) => file.status === 'done',
        );

        // 获取当前字段的上传状态
        const uploadStore = useUploadStore();
        const currentState = uploadStore.getFieldState(item.field);

        // 更新状态：总数 = 待上传 + 已完成，已上传数 = 已完成数
        const newTotalCount = pendingFiles.length + completedFiles.length;
        if (
          newTotalCount !== currentState.totalCount ||
          completedFiles.length !== currentState.uploadedCount
        ) {
          uploadStore.setFieldState(item.field, {
            totalCount: newTotalCount,
            uploadedCount: completedFiles.length,
            isUploading: pendingFiles.length > 0 && currentState.isUploading,
          });
        }

        // 调用原始的 onChange 处理函数
        originalOnChange?.(info);
      };

      // 如果后端配置了自定义上传函数，则使用后端配置
      if (uploadConfig.customRequest) {
        componentProps.customRequest = uploadConfig.customRequest;
      }

      // 设置渲染内容（上传按钮的文本）
      if (uploadConfig.uploadText || item.title) {
        schema.renderComponentContent = () => {
          return {
            default: () => uploadConfig.uploadText || `上传${item.title}`,
          };
        };
      }

      break;
    }

    default: {
      // 默认情况下的通用属性
      if (hasValue(config.placeholder)) {
        componentProps.placeholder = config.placeholder;
      }
      if (hasValue(config.allowClear)) {
        componentProps.allowClear = config.allowClear;
      }
      break;
    }
  }

  // 添加通用属性
  // disabled 属性需要特殊处理，因为 false 也是有效值
  // 优先使用外层的 disabled，如果没有则使用 config.disabled
  if (item.disabled !== undefined && item.disabled !== null) {
    componentProps.disabled = item.disabled;
  } else if (config.disabled !== undefined && config.disabled !== null) {
    componentProps.disabled = config.disabled;
  }

  if (hasValue(config.suffix)) {
    componentProps.suffix = config.suffix;
  }

  if (hasValue(config.prefix)) {
    componentProps.prefix = config.prefix;
  }

  // 只有当 componentProps 有内容时才添加到 schema 中
  if (Object.keys(componentProps).length > 0) {
    schema.componentProps = componentProps;
  }

  // 添加验证规则
  if (item.required || config.ruleType) {
    schema.rules =
      item.type === 'select' ||
      item.type === 'tree' ||
      item.type === 'radio' ||
      item.type === 'date' ||
      item.type === 'dateRange'
        ? config.ruleType || 'selectRequired'
        : config.ruleType || 'required';
  }

  // 处理联动赋值配置
  if (
    config.linkageAssignment &&
    (item.type === 'select' ||
      item.type === 'apiSelect' ||
      item.type === 'apiselect' ||
      item.type === 'tree' ||
      item.type === 'apiTree' ||
      item.type === 'radio')
  ) {
    // 使用 componentProps 函数形式处理联动赋值
    const originalComponentProps = schema.componentProps;

    schema.componentProps = (values: Record<string, any>, formApi?: any) => {
      // 合并原有的 componentProps
      const baseProps =
        typeof originalComponentProps === 'function'
          ? originalComponentProps(values, formApi)
          : originalComponentProps || {};

      return {
        ...baseProps,
        onChange: (value: string, selectedOption: any) => {
          console.log('[联动赋值] onChange triggered:', {
            field: item.field,
            value,
            selectedOption,
            hasFormApi: !!formApi,
            hasSetFieldValue: !!(formApi && formApi.setFieldValue),
          });

          // 检测是否为用户交互：检查表单是否正在初始化
          // 如果表单正在初始化，则认为不是用户交互
          const isUserInteraction = !isFormInitializing(formApi);

          console.log('[联动赋值] 交互类型检测:', {
            field: item.field,
            isUserInteraction,
            isFormInitializing: isFormInitializing(formApi),
            hasSelectedOption: !!selectedOption,
          });

          // 执行联动赋值逻辑
          const targetFields = config.linkageAssignment!.targetFields;

          targetFields.forEach((targetField) => {
            try {
              if (value === undefined || value === null || value === '') {
                // 当前字段为空时，根据配置决定是否清空目标字段
                if (targetField.clearOnEmpty !== false) {
                  // 使用 formApi.setFieldValue 来正确设置字段值，触发响应式更新
                  if (formApi && formApi.setFieldValue) {
                    formApi.setFieldValue(targetField.field, undefined);
                  } else {
                    // 备用方案：直接修改 values 对象
                    values[targetField.field] = undefined;
                  }
                }
              } else {
                // 当前字段有值时，计算目标字段的值
                let targetValue;

                if (typeof targetField.valueMapping === 'function') {
                  // 如果是函数，直接调用
                  targetValue = targetField.valueMapping(value, selectedOption);
                } else if (typeof targetField.valueMapping === 'string') {
                  // 直接使用字符串作为字段名从 selectedOption 中获取值
                  targetValue =
                    selectedOption?.[targetField.valueMapping] || null;
                } else if (
                  typeof targetField.valueMapping === 'object' &&
                  targetField.valueMapping !== null
                ) {
                  // 处理对象配置格式
                  if (targetField.valueMapping[value] === undefined) {
                    // 使用默认值
                    targetValue = targetField.valueMapping.default || null;
                  } else {
                    // 处理固定值映射：{ '1': 'manager_001', '2': 'manager_002', 'default': null }
                    targetValue =
                      targetField.valueMapping[value] ||
                      targetField.valueMapping.default ||
                      null;
                  }
                } else {
                  // 如果是其他类型的固定值，直接使用
                  targetValue = targetField.valueMapping;
                }

                // 使用 formApi.setFieldValue 来正确设置字段值，触发响应式更新
                if (formApi && formApi.setFieldValue) {
                  console.log(
                    `[联动赋值] 使用 formApi.setFieldValue 设置: ${targetField.field} = ${JSON.stringify(targetValue)}`,
                  );
                  formApi.setFieldValue(targetField.field, targetValue);
                  console.log(
                    `[联动赋值] 设置后的值: ${JSON.stringify(values[targetField.field])}`,
                  );

                  // 对于 apiSelect 等远程组件，设置值后需要强制刷新以确保正确显示标签
                  const targetFieldConfig = backendItems?.find(
                    (item) => item.field === targetField.field,
                  );
                  if (
                    targetFieldConfig &&
                    (targetFieldConfig.type === 'apiSelect' ||
                      targetFieldConfig.type === 'apiselect' ||
                      targetFieldConfig.type === 'apiTree')
                  ) {
                    console.log(
                      `[联动赋值] 检测到远程组件 ${targetField.field}，强制刷新以显示正确标签`,
                    );

                    const config = targetFieldConfig.config || {};

                    // 检查是否有 returnParamsField 配置
                    if (config.returnParamsField && targetValue) {
                      // 对于有 returnParamsField 的组件，通过 updateSchema 更新 params 来触发 API 请求
                      if (formApi.updateSchema) {
                        const newParams = {
                          ...config.params,
                          [config.returnParamsField]: targetValue,
                        };

                        // 延迟执行，确保值设置完成后再更新参数
                        setTimeout(() => {
                          const refreshKey = `refresh_${Date.now()}`;
                          formApi.updateSchema({
                            field: targetField.field,
                            componentProps: {
                              key: refreshKey,
                              params: newParams,
                              immediate: true,
                              alwaysLoad: true,
                            },
                          });
                        }, 50);
                      }
                    } else {
                      // 对于没有 returnParamsField 的组件，使用通用的刷新方式
                      if (formApi.updateSchema) {
                        setTimeout(() => {
                          const refreshKey = `refresh_${Date.now()}`;

                          // 先更新组件属性，强制重新渲染
                          formApi.updateSchema({
                            field: targetField.field,
                            componentProps: {
                              key: refreshKey,
                              immediate: true,
                              alwaysLoad: true,
                            },
                          });

                          // 再次确保值正确设置
                          if (formApi.setFieldValue) {
                            formApi.setFieldValue(
                              targetField.field,
                              targetValue,
                            );
                          }

                          // 尝试直接调用组件的API方法来强制刷新数据
                          if (formApi.getFieldComponentRef) {
                            setTimeout(() => {
                              const componentRef = formApi.getFieldComponentRef(
                                targetField.field,
                              );
                              if (
                                componentRef &&
                                typeof componentRef.updateParam === 'function'
                              ) {
                                // 触发参数更新，即使参数没有变化也会重新请求
                                componentRef.updateParam({
                                  _forceRefresh: Date.now(),
                                });
                              }
                            }, 100); // 再延迟100ms确保组件已经重新渲染
                          }
                        }, 50);
                      }
                    }
                  }
                } else {
                  // 备用方案：直接修改 values 对象
                  values[targetField.field] = targetValue;
                }

                // 处理动态选项配置
                if (targetField.options) {
                  let targetOptions: Array<{
                    [key: string]: any;
                    label: string;
                    value: any;
                  }> = [];
                  let clearValue = false;
                  let autoSelect = false;

                  // 支持直接数组格式和对象格式
                  if (Array.isArray(targetField.options)) {
                    // 直接数组格式：[{value: 'customer_id', label: 'customer_name'}]
                    targetOptions = targetField.options;
                  } else {
                    // 对象格式：{mapping: ..., default: ..., clearValue: ..., autoSelect: ...}
                    const optionsConfig = targetField.options;
                    clearValue = optionsConfig.clearValue || false;
                    autoSelect = optionsConfig.autoSelect || false;

                    // 使用类似 valueMapping 的逻辑来获取选项
                    if (typeof optionsConfig.mapping === 'function') {
                      // 如果是函数，直接调用
                      targetOptions =
                        optionsConfig.mapping(value, selectedOption) || [];
                    } else if (typeof optionsConfig.mapping === 'string') {
                      // 直接使用字符串作为字段名从 selectedOption 中获取选项
                      targetOptions =
                        selectedOption?.[optionsConfig.mapping] ||
                        optionsConfig.default ||
                        [];
                    } else if (
                      typeof optionsConfig.mapping === 'object' &&
                      optionsConfig.mapping !== null
                    ) {
                      // 处理对象配置格式
                      targetOptions =
                        value && optionsConfig.mapping[value]
                          ? optionsConfig.mapping[value]
                          : optionsConfig.default || [];
                    } else {
                      // 如果是其他类型的固定值，直接使用
                      targetOptions =
                        optionsConfig.mapping || optionsConfig.default || [];
                    }
                  }

                  // 设置目标字段的选项
                  if (formApi && formApi.updateSchema) {
                    formApi.updateSchema({
                      field: targetField.field,
                      componentProps: {
                        options: targetOptions,
                      },
                    });
                  }

                  // 如果配置了清空值或自动选择
                  if (clearValue && formApi && formApi.setFieldValue) {
                    formApi.setFieldValue(targetField.field, undefined);
                  } else if (
                    autoSelect &&
                    targetOptions.length > 0 &&
                    formApi &&
                    formApi.setFieldValue
                  ) {
                    // 自动选择第一个选项
                    formApi.setFieldValue(
                      targetField.field,
                      targetOptions[0].value,
                    );
                  }
                }

                // 检查目标字段是否是远程组件，如果是则触发API请求
                if (backendItems) {
                  triggerRemoteComponentRefresh(
                    targetField.field,
                    targetValue,
                    values,
                    backendItems,
                    isUserInteraction, // 传递用户交互状态
                  );
                }
              }
            } catch (error) {
              console.warn(
                `联动赋值失败: ${item.field} -> ${targetField.field}`,
                error,
              );
            }
          });

          // 调用原有的 onChange 事件
          if (baseProps.onChange) {
            baseProps.onChange(value, selectedOption);
          }
        },
      };
    };

    // 确保有 dependencies 配置，但不需要复杂的事件监听
    if (!schema.dependencies) {
      schema.dependencies = {
        triggerFields: [item.field],
      };
    } else if (!schema.dependencies.triggerFields.includes(item.field)) {
      schema.dependencies.triggerFields.push(item.field);
    }
  }

  // 处理联动配置 - 支持从 config.linkage 或顶级 linkage 读取
  const linkageConfig = config.linkage || item.linkage;
  if (linkageConfig) {
    const dependencies =
      LinkageRuleConverter.convertLinkageToVbenDependencies(linkageConfig);

    if (dependencies) {
      // 如果有 trigger，包装它以传递字段名
      if (dependencies.trigger) {
        const originalTrigger = dependencies.trigger;
        dependencies.trigger = (values: Record<string, any>, formApi?: any) => {
          // 为 formApi 添加字段名信息
          if (formApi) {
            formApi.fieldName = item.field;
          }
          return originalTrigger(values, formApi);
        };
      }

      // 如果有 componentProps，包装它以传递字段名
      if (dependencies.componentProps) {
        const originalComponentProps = dependencies.componentProps;
        dependencies.componentProps = (
          values: Record<string, any>,
          formApi?: any,
        ) => {
          // 为 formApi 添加字段名信息
          if (formApi) {
            formApi.fieldName = item.field;
          }
          return originalComponentProps(values, formApi);
        };
      }

      schema.dependencies = dependencies;
    }
  }

  // 清理 undefined 和 null 值
  const finalSchema = cleanUndefinedValues(schema);

  return finalSchema;
}

/**
 * 快速转换函数，直接返回可用的 schema
 * @param backendItems 后端搜索条件数组
 * @param formMode 表单模式：'add' | 'edit' | undefined
 * @returns 转换后的 schema 数组
 */
export function quickTransformSearch(
  backendItems: BackendSearchItem[],
  formMode?: 'add' | 'edit',
): VbenFormSchema[] {
  return transformBackendSearchToSchema(backendItems, { formMode });
}
