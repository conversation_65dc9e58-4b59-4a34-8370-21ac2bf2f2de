import type { BackendSearchItem } from './types';

/**
 * 联动赋值管理器 - 基于事件系统的清晰实现
 */
export class LinkageAssignmentManager {
  private static instance: LinkageAssignmentManager;
  private linkageConfigs = new Map<string, any>();
  
  static getInstance(): LinkageAssignmentManager {
    if (!LinkageAssignmentManager.instance) {
      LinkageAssignmentManager.instance = new LinkageAssignmentManager();
    }
    return LinkageAssignmentManager.instance;
  }

  /**
   * 注册联动配置
   */
  registerLinkage(sourceField: string, config: any, backendItems: BackendSearchItem[]) {
    this.linkageConfigs.set(sourceField, { config, backendItems });
  }

  /**
   * 执行联动赋值（只在用户交互时调用）
   */
  executeLinkage(sourceField: string, value: any, selectedOption: any, formApi: any) {
    const linkageData = this.linkageConfigs.get(sourceField);
    if (!linkageData) return;

    const { config, backendItems } = linkageData;
    const targetFields = config.linkageAssignment.targetFields;

    console.log('[联动赋值] 执行联动:', {
      sourceField,
      value,
      targetFields: targetFields.map((t: any) => t.field),
    });

    targetFields.forEach((targetField: any) => {
      try {
        if (value === undefined || value === null || value === '') {
          // 清空目标字段
          if (targetField.clearOnEmpty !== false && formApi?.setFieldValue) {
            formApi.setFieldValue(targetField.field, undefined);
          }
        } else {
          // 计算目标字段的值
          let targetValue = this.calculateTargetValue(targetField, value, selectedOption);
          
          // 设置目标字段的值
          if (formApi?.setFieldValue) {
            formApi.setFieldValue(targetField.field, targetValue);
            
            // 处理远程组件刷新
            this.handleRemoteComponentRefresh(targetField, targetValue, formApi, backendItems);
          }
        }
      } catch (error) {
        console.warn(`联动赋值失败: ${sourceField} -> ${targetField.field}`, error);
      }
    });
  }

  /**
   * 计算目标字段的值
   */
  private calculateTargetValue(targetField: any, value: any, selectedOption: any): any {
    if (typeof targetField.valueMapping === 'function') {
      return targetField.valueMapping(value, selectedOption);
    } else if (typeof targetField.valueMapping === 'string') {
      return selectedOption?.[targetField.valueMapping] || null;
    } else if (
      typeof targetField.valueMapping === 'object' &&
      targetField.valueMapping !== null
    ) {
      return targetField.valueMapping[value] || targetField.valueMapping.default || null;
    } else {
      return targetField.valueMapping;
    }
  }

  /**
   * 处理远程组件刷新
   */
  private handleRemoteComponentRefresh(
    targetField: any, 
    targetValue: any, 
    formApi: any, 
    backendItems: BackendSearchItem[]
  ) {
    const targetFieldConfig = backendItems.find(item => item.field === targetField.field);
    if (!targetFieldConfig) return;

    const isRemoteComponent = 
      targetFieldConfig.type === 'apiSelect' ||
      targetFieldConfig.type === 'apiselect' ||
      targetFieldConfig.type === 'apiTree';

    if (isRemoteComponent && formApi.updateSchema) {
      const config = targetFieldConfig.config || {};
      
      // 强制刷新远程组件
      setTimeout(() => {
        const refreshKey = `refresh_${Date.now()}`;
        formApi.updateSchema({
          field: targetField.field,
          componentProps: {
            key: refreshKey,
            immediate: true,
            alwaysLoad: true,
          },
        });
      }, 50);
    }
  }

  /**
   * 清理联动配置
   */
  clearLinkage(sourceField: string) {
    this.linkageConfigs.delete(sourceField);
  }
}

/**
 * 创建联动赋值的 componentProps
 */
export function createLinkageComponentProps(
  originalComponentProps: any,
  sourceField: string,
  itemType: string
) {
  const linkageManager = LinkageAssignmentManager.getInstance();

  return (values: Record<string, any>, formApi?: any) => {
    const baseProps =
      typeof originalComponentProps === 'function'
        ? originalComponentProps(values, formApi)
        : originalComponentProps || {};

    return {
      ...baseProps,
      onChange: (value: any, selectedOption: any) => {
        // 检测是否为用户交互
        const isUserInteraction = 
          selectedOption !== undefined || 
          itemType === 'radio' || 
          itemType === 'checkbox';
        
        // 只有在用户交互时才触发联动
        if (isUserInteraction) {
          linkageManager.executeLinkage(sourceField, value, selectedOption, formApi);
        }

        // 调用原有的 onChange 事件
        if (baseProps.onChange) {
          baseProps.onChange(value, selectedOption);
        }
      },
    };
  };
}
