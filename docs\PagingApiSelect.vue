<template>
  <Select
    @dropdown-visible-change="handleFetch"
    v-bind="getAttrs"
    @change="handleChange"
    v-model:value="state"
    :showArrow="true"
    @popup-scroll="handlePopupScroll"
  >
    <template #[item]="data" v-for="item in Object.keys($slots)">
      <slot :name="item" v-bind="data || {}"></slot>
    </template>
    <template #suffixIcon v-if="loading">
      <LoadingOutlined spin />
    </template>
    <template #notFoundContent v-if="loading">
      <span>
        <LoadingOutlined spin class="mr-1" />
        {{ t('component.form.apiSelectNotFound') }}
      </span>
    </template>
  </Select>
</template>
<script lang="ts" setup name="ApiSelect" inheritAttrs="false">
import { ref, watchEffect, computed, unref, watch, reactive, onMounted } from 'vue'
import { Select, SelectProps } from 'ant-design-vue'
import { isArray, isEmpty, isFunction } from '/@/utils/is'
import { useRuleFormItem } from '/@/hooks/component/useFormItem'
import { useAttrs } from '/@/hooks/core/useAttrs'
import { get, set, omit, trim } from 'lodash-es'
import { LoadingOutlined } from '@ant-design/icons-vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { propTypes } from '/@/utils/propTypes'
import { SelectValue } from 'ant-design-vue/lib/select'
import { useDebounceFn } from '@vueuse/core'
import { add } from '/@/utils/math'

const props = defineProps({
  value: {
    type: [Array, Object, String, Number] as PropType<SelectValue>
  },
  numberToString: propTypes.bool,
  api: {
    type: Function as PropType<(arg?: Recordable) => Promise<Recordable[]>>
  },
  // api params
  params: {
    type: Object as PropType<Recordable>,
    default: () => ({})
  },
  // support xxx.xxx.xx
  resultField: propTypes.string.def(''),
  alwaysLoad: propTypes.bool.def(true),
  selectProps: { type: Object as PropType<SelectProps> },

  // 编辑
  searchMode: {
    type: Boolean,
    default: false
  },
  immediate: propTypes.bool.def(true),
  defaultOptions: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  searchParamField: propTypes.string.def('name'),
  // options里面第一个是否有一个为'空'的选项，目的是为了筛选资金流水客户或供应商为空时;
  itEmpty: {
    type: Boolean,
    default: false
  },
  // 分页模式
  pagingMode: {
    type: Boolean,
    default: false
  },
  pagingSize: propTypes.number.def(20),
  returnParamsField: propTypes.string.def('name'),
  returnApi: {
    type: Function as PropType<(arg?: Recordable) => Promise<Recordable[]>>
  },
  returnResultField: propTypes.string.def(''),
  // 禁用特定选项的配置
  disabledOptions: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  // 禁用选项的判断字段，默认使用 id
  disabledField: propTypes.string.def('id'),
  // 自定义禁用判断函数
  disabledFunction: {
    type: Function as PropType<(option: Recordable) => boolean>
  }
})
const emit = defineEmits(['options-change', 'change', 'update:value'])
const paging = reactive({
  page: 1,
  pageSize: props.pagingSize
})

const options = ref<Recordable[]>([])
const loading = ref(false)
const isFirstLoaded = ref<Boolean>(false)

const emitData = ref<any[]>([])
const searchValue = ref<string>('')

const attrs = useAttrs()
const { t } = useI18n()

const getAttrs = computed(() => {
  let newAttrs = {
    ...(props.api ? { options: unref(options) } : {}),
    ...omit(props.selectProps, ['options']),
    ...unref(attrs)
  }

  //为了配合远程搜索模式配合设定的参数
  if (props.searchMode) {
    newAttrs.filterOption = false
    newAttrs.showSearch = true
    newAttrs.onSearch = useDebounceFn(handleSearch, 500)
  }

  return newAttrs
})

// Embedded in the form, just use the hook binding to perform form verification
const [state] = useRuleFormItem(props, 'value', 'change', emitData)

watchEffect(() => {
  props.alwaysLoad && !props.searchMode && fetch()
})

watch(
  () => state.value,
  (v) => {
    emit('update:value', v)
  }
)

//既保留了原来控件的外部修改params可以触发修改
watch(
  () => [props.params, searchValue],
  () => {
    if (!props.searchMode || searchValue.value.length > 0) {
      !unref(isFirstLoaded) && fetch()
    }
  },
  { deep: true }
)

function getFetchParams() {
  let params = props.params

  if (props.searchMode) {
    // 判断searchValue是否为空，为空的话就就把字段删除
    if (isEmpty(searchValue.value)) {
      delete params[props.searchParamField]
    } else {
      set(params, props.searchParamField, searchValue.value)
    }

    if (props.pagingMode) {
      set(params, 'page', paging.page)
      set(params, 'pageSize', paging.pageSize)
    }
  }
  return params
}

async function fetch() {
  isFirstLoaded.value = true

  if (!props.immediate) {
    if (!props.searchMode) {
      return
    }

    if (isEmpty(searchValue.value)) {
      options.value = props.defaultOptions
      return
    }
  }

  const api = props.api
  if (!api || !isFunction(api)) return

  loading.value = true
  options.value = []
  let result
  try {
    const params = getFetchParams()
    result = await api(params)
  } catch (error) {
    console.warn(error)
  } finally {
    loading.value = false
  }
  if (!result) return
  if (!isArray(result)) {
    result = get(result, props.resultField)
  }
  options.value = (result as Recordable[]) || []

  // 处理禁用选项
  if (props.disabledOptions.length > 0 || props.disabledFunction) {
    options.value = options.value.map((option) => {
      let disabled = false

      // 使用自定义禁用函数
      if (props.disabledFunction) {
        disabled = props.disabledFunction(option)
      }
      // 使用禁用选项数组
      else if (props.disabledOptions.length > 0) {
        const fieldValue = get(option, props.disabledField)
        disabled = props.disabledOptions.includes(fieldValue)
      }

      return {
        ...option,
        disabled: disabled || option.disabled // 保留原有的 disabled 属性
      }
    })
  }

  // 做了修改（hDev2）
  if (props.itEmpty) {
    options.value.unshift({ name: '空', id: 0 })
  }

  if (props.pagingMode && props.searchMode) {
    paging.page += 1
  }

  emit('options-change', options.value)
}

async function handleFetch(visible) {
  if (visible) {
    if (props.alwaysLoad || props.searchMode || !unref(isFirstLoaded)) {
      if (props.searchMode && props.pagingMode) {
        paging.page = 1
      }
      await fetch()
    }
  } else {
    searchValue.value = ''
  }
}

async function handleSearch(value: string) {
  value = trim(value)
  //当搜索框输入的值不为空的时候才触发内容修改
  if (searchValue.value != value && !isEmpty(value)) {
    if (props.searchMode && props.pagingMode) {
      paging.page = 1
    }
    searchValue.value = value
    await fetch()
  }
}

function handleChange(_, ...args) {
  //加入labelInValue选定了，那么这里args就是一个对象
  // console.log('change', value, args)
  emitData.value = args
}

async function handlePopupScroll(e) {
  const target = e.target
  if (target.scrollHeight === add(target.scrollTop, target.offsetHeight) && props.searchMode && props.pagingMode) {
    if (loading.value) return
    try {
      const api = props.api
      if (!api || !isFunction(api)) return

      loading.value = true
      // options.value = []
      let result
      try {
        const params = getFetchParams()
        result = await api(params)
      } catch (error) {
        console.warn(error)
      } finally {
        loading.value = false
      }
      if (!result) return
      if (!isArray(result)) {
        result = get(result, props.resultField)
      }
      // options.value = (result as Recordable[]) || []
      let newOptions = result as Recordable[]

      // 处理禁用选项
      if (props.disabledOptions.length > 0 || props.disabledFunction) {
        newOptions = newOptions.map((option) => {
          let disabled = false

          // 使用自定义禁用函数
          if (props.disabledFunction) {
            disabled = props.disabledFunction(option)
          }
          // 使用禁用选项数组
          else if (props.disabledOptions.length > 0) {
            const fieldValue = get(option, props.disabledField)
            disabled = props.disabledOptions.includes(fieldValue)
          }

          return {
            ...option,
            disabled: disabled || option.disabled // 保留原有的 disabled 属性
          }
        })
      }

      options.value = [...options.value, ...newOptions]

      if (props.pagingMode && props.searchMode) {
        paging.page += 1
      }

      emit('options-change', options.value)
    } catch (e) {
      console.log(e)
    }
  }
}

// 由于vue版本低，无法使用watch.once属性，所哟用变量判断是否首次渲染
let watchFlag = true
// 监听首次进入的value值，如果有值就
onMounted(() => {
  watch(
    () => props.defaultOptions,
    (val) => {
      if (val && val.length > 0) {
        options.value = [...options.value, ...val]
      }
    }
  )
  watch(
    [() => props.value, () => props.pagingMode],
    (newVal) => {
      const [value, pagingMode] = newVal
      if (value && pagingMode && watchFlag) {
        returnFetch()
      }
      watchFlag = false
    },
    { immediate: true, deep: true }
  )
})

async function returnFetch() {
  const api = props.returnApi || props.api
  // 如果有returnResultField就用returnResultField，没值就用resultField
  const field = props.returnResultField || props.resultField
  const paramsField = props.returnParamsField || props.searchParamField
  if (!api || !isFunction(api)) return

  loading.value = true
  options.value = []
  let result
  try {
    // mode: 'multiple' 请求多个
    if (isArray(props.value) && props?.selectProps?.mode === 'multiple') {
      result = await Promise.allSettled(props.value.map((item) => api({ ...props.params, [paramsField]: item })))
      // console.log(result)
      const response = result.map((item) => item.value[field])
      let optList = []
      try {
        for (const item of response) {
          if (!isArray(item)) {
            optList.push(get(item, field))
            continue
          }
          optList = optList.concat(item)
        }
      } catch (e) {
        console.log(e)
      }
      options.value = optList
      console.log(options.value)
      return
    }
    // mode: '' 请求单个
    const params = { ...props.params, [paramsField]: props.value }
    result = await api(params)
  } catch (error) {
    throw new Error(`${error}`)
  } finally {
    loading.value = false
  }
  if (!result) return
  if (!isArray(result)) {
    result = get(result, field)
  }
  options.value = (result as Recordable[]) || []

  emit('options-change', options.value)
}
</script>
